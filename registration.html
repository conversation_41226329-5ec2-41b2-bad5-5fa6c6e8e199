<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Page</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .container {
            position: relative;
            width: 450px;
            height: 700px;
            perspective: 1000px;
        }

        .form-container {
            position: absolute;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            padding: 40px;
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        h2 {
            text-align: center;
            margin-bottom: 25px;
            color: #333;
            font-size: 26px;
            font-weight: 600;
        }

        .input-group {
            position: relative;
            margin-bottom: 20px;
        }

        .input-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
        }

        .input-group select {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
            cursor: pointer;
        }

        .input-group input:focus,
        .input-group select:focus {
            outline: none;
            border-color: #667eea;
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .input-group label {
            position: absolute;
            top: 15px;
            left: 20px;
            color: #999;
            transition: all 0.3s ease;
            pointer-events: none;
            font-size: 16px;
        }

        .input-group input:focus + label,
        .input-group input:valid + label,
        .input-group select:focus + label,
        .input-group select:valid + label {
            top: -10px;
            left: 15px;
            font-size: 12px;
            color: #667eea;
            background: white;
            padding: 0 5px;
        }

        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .switch-text {
            display: flex;
            justify-content: center;
            text-align: center;
            color: #666;
            font-size: 14px;
        }

        .switch-link {
            text-align: center;
            margin-top: 10px;
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .switch-link:hover {
            
            color: #764ba2;
        }

        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .shape {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 60px;
            height: 60px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 100px;
            height: 100px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .form-enter {
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .back-button {
            display: block;
            width: 100%;
            text-align: center;
            margin-top: 15px;
            color: white;
            text-decoration: none;
            background: rgba(102, 126, 234, 0.2);
            padding: 12px 20px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            font-weight: 600;
            font-size: 14px;
        }

        .back-button:hover {
            background: rgba(102, 126, 234, 0.3);
            border-color: rgba(102, 126, 234, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body>
    <!-- <a href="index.html" class="back-button">← Back to Login</a> -->
    
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="container">
        <div class="form-container form-enter" id="formContainer">
            <h2>Create Account</h2>
            <form id="registerForm">
                <div class="input-group">
                    <input type="text" id="fullName" required>
                    <label for="fullName">Full Name</label>
                </div>
                <div class="input-group">
                    <input type="text" id="studentId" required>
                    <label for="studentId">Student ID</label>
                </div>
                <div class="input-group">
                    <input type="email" id="email" required>
                    <label for="email">Email Address</label>
                </div>
                <div class="input-group">
                    <input type="tel" id="mobile" required>
                    <label for="mobile">Mobile Number</label>
                </div>
                <div class="input-group">
                    <input type="text" id="address" required>
                    <label for="address">Address</label>
                </div>
                <div class="input-group">
                    <select id="department" required>
                        <option value="" disabled selected></option>
                        <option value="Computer Science">Computer Science</option>
                        <option value="Software Engineering">Software Engineering</option>
                        <option value="Information Technology">Information Technology</option>
                        <option value="Electrical Engineering">Electrical Engineering</option>
                        <option value="Mechanical Engineering">Mechanical Engineering</option>
                        <option value="Civil Engineering">Civil Engineering</option>
                        <option value="Business Administration">Business Administration</option>
                        <option value="Economics">Economics</option>
                        <option value="Mathematics">Mathematics</option>
                        <option value="Physics">Physics</option>
                        <option value="Chemistry">Chemistry</option>
                        <option value="Biology">Biology</option>
                    </select>
                    <label for="department">Department</label>
                </div>
                <div class="input-group">
                    <select id="semester" required>
                        <option value="" disabled selected></option>
                        <option value="1st Semester">1st Semester</option>
                        <option value="2nd Semester">2nd Semester</option>
                        <option value="3rd Semester">3rd Semester</option>
                        <option value="4th Semester">4th Semester</option>
                        <option value="5th Semester">5th Semester</option>
                        <option value="6th Semester">6th Semester</option>
                        <option value="7th Semester">7th Semester</option>
                        <option value="8th Semester">8th Semester</option>
                        <option value="8th Semester">9th Semester</option>
                        <option value="10th Semester">10th Semester</option>
                        <option value="11th Semester">11th Semester</option>
                        <option value="12th Semester">12th Semester</option>
                    </select>
                    <label for="semester">Semester</label>
                </div>
                <button type="submit" class="btn">Create Account</button>
            </form>
            <div class="switch-text">
                Already have an account? 
                <a href="index.html" class="switch-link">Sign in</a>
            </div>
            <!-- <a href="index.html" class="back-button full-width">← Back to Login</a> -->
        </div>
    </div>

    <script>
        // Add form enter animation
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.getElementById('formContainer');
            container.classList.add('form-enter');
        });

        // Handle form submission with validation
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const fullName = document.getElementById('fullName').value.trim();
            const studentId = document.getElementById('studentId').value.trim();
            const email = document.getElementById('email').value.trim();
            const mobile = document.getElementById('mobile').value.trim();
            const address = document.getElementById('address').value.trim();
            const department = document.getElementById('department').value;
            const semester = document.getElementById('semester').value;
            
            // Basic validation
            if (!fullName || !studentId || !email || !mobile || !address || !department || !semester) {
                alert('Please fill in all required fields!');
                return;
            }
            
            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                alert('Please enter a valid email address!');
                return;
            }
            
            // Mobile validation (basic)
            const mobileRegex = /^[0-9]{10,15}$/;
            if (!mobileRegex.test(mobile.replace(/\s/g, ''))) {
                alert('Please enter a valid mobile number (10-15 digits)!');
                return;
            }
            
            // Student ID validation (basic)
            if (studentId.length < 4) {
                alert('Student ID must be at least 4 characters long!');
                return;
            }
            
            // Simulate successful registration
            alert(`Registration successful!\n\nWelcome ${fullName}!\nStudent ID: ${studentId}\nDepartment: ${department}\nSemester: ${semester}`);
            
            // Redirect to login page after short delay
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 2000);
        });

        // Add input validation visual feedback
        document.querySelectorAll('.input-group input, .input-group select').forEach(element => {
            element.addEventListener('blur', function() {
                if (this.value.trim() === '') {
                    this.style.borderColor = '#e74c3c';
                } else {
                    this.style.borderColor = '#2ecc71';
                }
            });
            
            element.addEventListener('focus', function() {
                this.style.borderColor = '#667eea';
            });
            
            // For select elements, also handle change event
            if (element.tagName === 'SELECT') {
                element.addEventListener('change', function() {
                    if (this.value === '') {
                        this.style.borderColor = '#e74c3c';
                    } else {
                        this.style.borderColor = '#2ecc71';
                    }
                });
            }
        });
    </script>
</body>
</html>
