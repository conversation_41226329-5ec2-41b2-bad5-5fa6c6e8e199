


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login & Registration</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .container {
            position: relative;
            width: 400px;
            height: 500px;
            perspective: 1000px;
        }

        .form-container {
            position: absolute;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            padding: 40px;
            transform-style: preserve-3d;
            transition: transform 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            backdrop-filter: blur(10px);
        }

        .form-container.flip {
            transform: rotateY(180deg);
        }

        .login-form, .register-form {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            padding: 40px;
            backface-visibility: hidden;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .register-form {
            transform: rotateY(180deg);
        }

        h2 {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
            font-size: 28px;
            font-weight: 600;
        }

        .input-group {
            position: relative;
            margin-bottom: 25px;
        }

        .input-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
        }

        .input-group input:focus {
            outline: none;
            border-color: #667eea;
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .input-group label {
            position: absolute;
            top: 15px;
            left: 20px;
            color: #999;
            transition: all 0.3s ease;
            pointer-events: none;
            font-size: 16px;
        }

        .input-group input:focus + label,
        .input-group input:valid + label {
            top: -10px;
            left: 15px;
            font-size: 12px;
            color: #667eea;
            background: white;
            padding: 0 5px;
        }

        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .switch-text {
            text-align: center;
            color: #666;
            font-size: 14px;
        }

        .switch-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .switch-link:hover {
            color: #764ba2;
        }

        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .shape {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 60px;
            height: 60px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 100px;
            height: 100px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .form-enter {
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .error-message {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 5px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .error-message.show {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="container">
        <div class="form-container" id="formContainer">
            <!-- Login Form -->
            <div class="login-form">
                <h2>Welcome Back</h2>
                <form id="loginForm">
                    <div class="input-group">
                        <input type="email" id="loginEmail" required>
                        <label for="loginEmail">Email</label>
                    </div>
                    <div class="input-group">
                        <input type="password" id="loginPassword" required>
                        <label for="loginPassword">Password</label>
                    </div>
                    <button type="submit" class="btn">Sign In</button>
                </form>
                <div class="switch-text">
                    Don't have an account? 
                    <a href="registration.html" class="switch-link"> Sign up</a>
                </div>
            </div>

            <!-- Registration Form -->
            <div class="register-form">
                <h2>Create Account</h2>
                <form id="registerForm">
                    <div class="input-group">
                        <input type="text" id="registerName" required>
                        <label for="registerName">Full Name</label>
                    </div>
                    <div class="input-group">
                        <input type="email" id="registerEmail" required>
                        <label for="registerEmail">Email</label>
                    </div>
                    <div class="input-group">
                        <input type="password" id="registerPassword" required>
                        <label for="registerPassword">Password</label>
                    </div>
                    <button type="submit" class="btn">Sign Up</button>
                </form>
                <div class="switch-text">
                    Already have an account? 
                    <a href="#" class="switch-link" onclick="switchToLogin()">Sign in</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchToRegister() {
            const container = document.getElementById('formContainer');
            container.classList.add('flip');
        }

        function switchToLogin() {
            const container = document.getElementById('formContainer');
            container.classList.remove('flip');
        }

        // Add form enter animation
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.getElementById('formContainer');
            container.classList.add('form-enter');
        });

        // Handle form submissions (basic validation)
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            if (email && password) {
                alert('Login functionality would be implemented here!');
            }
        });

        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const name = document.getElementById('registerName').value;
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            
            if (name && email && password) {
                alert('Registration functionality would be implemented here!');
            }
        });
    </script>
</body>
</html>
