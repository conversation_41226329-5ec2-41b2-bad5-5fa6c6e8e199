
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Library Management Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #333;
        }

        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 250px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .logo {
            text-align: center;
            padding: 20px;
            margin-bottom: 30px;
            border-bottom: 1px solid #eee;
        }

        .logo h2 {
            color: #2a5298;
            font-size: 24px;
            font-weight: 700;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin: 5px 0;
        }

        .nav-link {
            display: block;
            padding: 15px 25px;
            color: #666;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .nav-link:hover, .nav-link.active {
            background: #f8f9ff;
            color: #2a5298;
            border-left-color: #2a5298;
            transform: translateX(5px);
        }

        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 30px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2a5298;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .welcome-text {
            color: #666;
            font-size: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .stat-title {
            color: #666;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #2a5298;
            margin-bottom: 5px;
        }

        .stat-change {
            font-size: 12px;
            color: #28a745;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 25px;
        }

        .recent-activity, .quick-actions {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            color: #2a5298;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: #e3f2fd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 16px;
            color: #2a5298;
        }

        .activity-text {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .activity-time {
            font-size: 12px;
            color: #999;
        }

        .action-btn {
            display: block;
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
            color: white;
            text-decoration: none;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(42, 82, 152, 0.4);
        }

        .logout-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in {
            animation: fadeInUp 0.6s ease-out;
        }

        .fade-in-delay-1 {
            animation: fadeInUp 0.6s ease-out 0.1s both;
        }

        .fade-in-delay-2 {
            animation: fadeInUp 0.6s ease-out 0.2s both;
        }

        .fade-in-delay-3 {
            animation: fadeInUp 0.6s ease-out 0.3s both;
        }
    </style>
</head>
<body>
    <a href="index.html" class="logout-btn">← Back to Login</a>
    
    <div class="dashboard-container">
        <aside class="sidebar fade-in">
            <div class="logo">
                <h2>📚 Library</h2>
            </div>
            <nav>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link active" onclick="showSection('dashboard')">🏠 Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('books')">📖 Books</a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('members')">👥 Members</a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('transactions')">📋 Transactions</a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('reports')">📊 Reports</a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('settings')">⚙️ Settings</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <main class="main-content">
            <header class="header fade-in-delay-1">
                <h1>Dashboard</h1>
                <p class="welcome-text">Welcome back! Here's what's happening in your library today.</p>
            </header>

            <section class="stats-grid fade-in-delay-2">
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Total Books</span>
                        <div class="stat-icon" style="background: #4CAF50;">📚</div>
                    </div>
                    <div class="stat-number">2,547</div>
                    <div class="stat-change">+12 this month</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Active Members</span>
                        <div class="stat-icon" style="background: #2196F3;">👥</div>
                    </div>
                    <div class="stat-number">1,248</div>
                    <div class="stat-change">+8 this week</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Books Issued</span>
                        <div class="stat-icon" style="background: #FF9800;">📋</div>
                    </div>
                    <div class="stat-number">342</div>
                    <div class="stat-change">+15 today</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Overdue Books</span>
                        <div class="stat-icon" style="background: #f44336;">⚠️</div>
                    </div>
                    <div class="stat-number">23</div>
                    <div class="stat-change">-5 this week</div>
                </div>
            </section>

            <section class="content-grid fade-in-delay-3">
                <div class="recent-activity">
                    <h3 class="section-title">Recent Activity</h3>
                    <div class="activity-item">
                        <div class="activity-icon">📚</div>
                        <div class="activity-text">
                            <div class="activity-title">New book added: "JavaScript: The Good Parts"</div>
                            <div class="activity-time">2 hours ago</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">👤</div>
                        <div class="activity-text">
                            <div class="activity-title">New member registered: John Smith</div>
                            <div class="activity-time">4 hours ago</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">📋</div>
                        <div class="activity-text">
                            <div class="activity-title">Book returned: "Clean Code" by Sarah Johnson</div>
                            <div class="activity-time">6 hours ago</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">⚠️</div>
                        <div class="activity-text">
                            <div class="activity-title">Overdue reminder sent to Mike Davis</div>
                            <div class="activity-time">1 day ago</div>
                        </div>
                    </div>
                </div>

                <div class="quick-actions">
                    <h3 class="section-title">Quick Actions</h3>
                    <a href="#" class="action-btn" onclick="quickAction('add-book')">📚 Add New Book</a>
                    <a href="#" class="action-btn" onclick="quickAction('add-member')">👤 Register Member</a>
                    <a href="#" class="action-btn" onclick="quickAction('issue-book')">📋 Issue Book</a>
                    <a href="#" class="action-btn" onclick="quickAction('return-book')">↩️ Return Book</a>
                    <a href="#" class="action-btn" onclick="quickAction('generate-report')">📊 Generate Report</a>
                </div>
            </section>
        </main>
    </div>

    <script>
        function showSection(section) {
            // Remove active class from all nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // Add active class to clicked link
            event.target.classList.add('active');
            
            // Update header based on section
            const header = document.querySelector('.header h1');
            const welcomeText = document.querySelector('.welcome-text');
            
            switch(section) {
                case 'dashboard':
                    header.textContent = 'Dashboard';
                    welcomeText.textContent = "Welcome back! Here's what's happening in your library today.";
                    break;
                case 'books':
                    header.textContent = 'Books Management';
                    welcomeText.textContent = 'Manage your library\'s book collection.';
                    break;
                case 'members':
                    header.textContent = 'Members Management';
                    welcomeText.textContent = 'Manage library members and their information.';
                    break;
                case 'transactions':
                    header.textContent = 'Transaction History';
                    welcomeText.textContent = 'View and manage book transactions.';
                    break;
                case 'reports':
                    header.textContent = 'Reports & Analytics';
                    welcomeText.textContent = 'Generate and view library reports.';
                    break;
                case 'settings':
                    header.textContent = 'System Settings';
                    welcomeText.textContent = 'Configure your library management system.';
                    break;
            }
        }

        function quickAction(action) {
            const actions = {
                'add-book': 'Add New Book form would open here!',
                'add-member': 'Register Member form would open here!',
                'issue-book': 'Issue Book form would open here!',
                'return-book': 'Return Book form would open here!',
                'generate-report': 'Report generation would start here!'
            };
            
            alert(actions[action] || 'Feature coming soon!');
        }

        // Add some interactive animations on load
        document.addEventListener('DOMContentLoaded', function() {
            // Animate stat cards on hover
            document.querySelectorAll('.stat-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>